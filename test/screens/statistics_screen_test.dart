import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:dreamflow/models/transaction_model.dart';
import 'package.money_lover_chat/screens/statistics_screen.dart';
import 'package:provider/provider.dart';

import '../helpers/test_helpers.dart';

void main() {
  group('StatisticsScreen Widget Tests', () {
    late TransactionProvider mockTransactionProvider;

    setUp(() {
      // Create a provider with some initial data
      mockTransactionProvider = TestHelpers.createMockTransactionProvider(
        transactions: [
          TestHelpers.createTestTransaction(
              amount: -50, categoryId: 'food', date: DateTime.now()),
          TestHelpers.createTestTransaction(
              amount: -30, categoryId: 'transport', date: DateTime.now()),
          TestHelpers.createTestTransaction(
              amount: 2000,
              type: TransactionType.income,
              categoryId: 'salary',
              date: DateTime.now()),
        ],
      );
    });

    Future<void> pumpScreen(WidgetTester tester) async {
      await tester.pumpWidget(
        ChangeNotifierProvider.value(
          value: mockTransactionProvider,
          child: const MaterialApp(
            home: StatisticsScreen(),
          ),
        ),
      );
      // pumpAndSettle to allow animations and futures to complete
      await tester.pumpAndSettle();
    }

    testWidgets('renders correctly with data', (WidgetTester tester) async {
      await pumpScreen(tester);

      // Verify title is displayed
      expect(find.text('Statistics'), findsOneWidget);

      // Verify summary cards are displayed
      expect(find.text('Balance'), findsOneWidget);
      expect(find.text('Income'), findsOneWidget);
      expect(find.text('Expense'), findsOneWidget);

      // Check for chart and transaction list toggle buttons
      expect(find.byIcon(Icons.pie_chart), findsOneWidget);
      expect(find.byIcon(Icons.list), findsOneWidget);

      // Verify that the PieChart is visible by default
      expect(find.byType(PieChart), findsOneWidget);
    });

    testWidgets('displays correct summary values', (WidgetTester tester) async {
      await pumpScreen(tester);

      // Balance = 2000 - 50 - 30 = 1920
      expect(find.text('\$1,920.00'), findsOneWidget);
      // Income = 2000
      expect(find.text('\$2,000.00'), findsOneWidget);
      // Expense = 50 + 30 = 80
      expect(find.text('-\$80.00'), findsOneWidget);
    });

    testWidgets('toggling to list view shows transactions', (WidgetTester tester) async {
      await pumpScreen(tester);

      // Chart is visible initially
      expect(find.byType(PieChart), findsOneWidget);
      expect(find.byType(ListView), findsNothing);

      // Tap the list view toggle
      await tester.tap(find.byIcon(Icons.list));
      await tester.pumpAndSettle();

      // Now, ListView should be visible and PieChart should be gone
      expect(find.byType(PieChart), findsNothing);
      expect(find.byType(ListView), findsOneWidget);

      // Verify transaction items are in the list
      expect(find.text('Food & Drink'), findsOneWidget);
      expect(find.text('Transport'), findsOneWidget);
      expect(find.text('Salary'), findsOneWidget);
    });

    testWidgets('displays empty state when there are no transactions', (WidgetTester tester) async {
      // Create a provider with no transactions
      mockTransactionProvider = TestHelpers.createMockTransactionProvider();
      await pumpScreen(tester);

      expect(find.text('No transactions yet.'), findsOneWidget);
      expect(find.text('Add a transaction from the Chat screen to see your stats.'), findsOneWidget);
      
      // Verify summary cards show zero
      expect(find.text('\$0.00'), findsNWidgets(2)); // Balance and Income
      expect(find.text('-\$0.00'), findsOneWidget); // Expense
    });
  });
}
