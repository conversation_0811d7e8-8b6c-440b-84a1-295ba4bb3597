import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:dreamflow/navigation/app_navigation.dart';

void main() {
  group('AppNavigation Widget Tests', () {
    testWidgets('should render AppNavigation widget', (WidgetTester tester) async {
      // Simple test to verify the widget can be created
      await tester.pumpWidget(
        const MaterialApp(
          home: AppNavigation(),
        ),
      );

      // Just verify the widget renders without crashing
      expect(find.byType(AppNavigation), findsOneWidget);
    });

    testWidgets('should have bottom navigation bar', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: AppNavigation(),
        ),
      );

      // Check for navigation bar
      expect(find.byType(NavigationBar), findsOneWidget);
    });
  });
}
