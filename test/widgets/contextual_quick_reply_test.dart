import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:dreamflow/widgets/quick_reply_widget.dart';
import 'package:dreamflow/models/parse_result.dart';

void main() {
  group('Contextual QuickReplyWidget Tests', () {
    testWidgets('should maintain backward compatibility with basic QuickReplyWidget', (WidgetTester tester) async {
      String? selectedOption;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: QuickReplyWidget(
              replyOptions: const ['Option 1', 'Option 2'],
              onReplySelected: (option) {
                selectedOption = option;
              },
            ),
          ),
        ),
      );

      // Verify options are displayed
      expect(find.text('Option 1'), findsOneWidget);
      expect(find.text('Option 2'), findsOneWidget);

      // Test interaction
      await tester.tap(find.text('Option 1'));
      expect(selectedOption, equals('Option 1'));
    });

    group('QuickReplyWidget with Icons and Colors', () {
      testWidgets('should display icons when showIcons is true', (WidgetTester tester) async {
        final options = [
          const QuickReplyOption(
            text: 'Save',
            icon: Icons.save,
            isPrimary: true,
          ),
          const QuickReplyOption(
            text: 'Cancel',
            icon: Icons.cancel,
          ),
        ];

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: QuickReplyWidget.withIcons(
                options: options,
                onReplySelected: (option) {},
              ),
            ),
          ),
        );

        // Verify icons are displayed
        expect(find.byIcon(Icons.save), findsOneWidget);
        expect(find.byIcon(Icons.cancel), findsOneWidget);
        expect(find.text('Save'), findsOneWidget);
        expect(find.text('Cancel'), findsOneWidget);
      });

      testWidgets('should apply custom colors from QuickReplyOption', (WidgetTester tester) async {
        final options = [
          const QuickReplyOption(
            text: 'Primary',
            backgroundColor: Colors.blue,
            textColor: Colors.white,
            isPrimary: true,
          ),
          const QuickReplyOption(
            text: 'Secondary',
            backgroundColor: Colors.grey,
            textColor: Colors.black,
          ),
        ];

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: QuickReplyWidget.withIcons(
                options: options,
                onReplySelected: (option) {},
              ),
            ),
          ),
        );

        // Find the buttons and verify colors are applied
        final primaryButton = find.widgetWithText(ElevatedButton, 'Primary');
        final secondaryButton = find.widgetWithText(ElevatedButton, 'Secondary');
        
        expect(primaryButton, findsOneWidget);
        expect(secondaryButton, findsOneWidget);
      });
    });

    group('Priority Grouping Logic', () {
      testWidgets('should group primary options first when groupByPriority is true', (WidgetTester tester) async {
        final options = [
          const QuickReplyOption(text: 'Secondary 1', isPrimary: false),
          const QuickReplyOption(text: 'Primary 1', isPrimary: true),
          const QuickReplyOption(text: 'Secondary 2', isPrimary: false),
          const QuickReplyOption(text: 'Primary 2', isPrimary: true),
        ];

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: QuickReplyWidget(
                replyOptions: options.map((opt) => opt.text).toList(),
                enhancedOptions: options,
                onReplySelected: (option) {},
                groupByPriority: true,
                showIcons: true,
              ),
            ),
          ),
        );

        // Verify all options are present
        expect(find.text('Primary 1'), findsOneWidget);
        expect(find.text('Primary 2'), findsOneWidget);
        expect(find.text('Secondary 1'), findsOneWidget);
        expect(find.text('Secondary 2'), findsOneWidget);
      });

      testWidgets('should maintain original order when groupByPriority is false', (WidgetTester tester) async {
        final options = [
          const QuickReplyOption(text: 'First', isPrimary: false),
          const QuickReplyOption(text: 'Second', isPrimary: true),
          const QuickReplyOption(text: 'Third', isPrimary: false),
        ];

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: QuickReplyWidget(
                replyOptions: options.map((opt) => opt.text).toList(),
                enhancedOptions: options,
                onReplySelected: (option) {},
                groupByPriority: false,
              ),
            ),
          ),
        );

        // Verify all options are present (order testing requires widget inspection)
        expect(find.text('First'), findsOneWidget);
        expect(find.text('Second'), findsOneWidget);
        expect(find.text('Third'), findsOneWidget);
      });
    });

    group('Ambiguity-Aware Quick Reply Variants', () {
      testWidgets('should create appropriate options for missingAmount ambiguity', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: QuickReplyWidget.forAmbiguityType(
                ambiguityType: AmbiguityType.missingAmount,
                options: const ['\$50', '\$100', 'Enter Amount'],
                onReplySelected: (option) {},
              ),
            ),
          ),
        );

        // Verify options are displayed
        expect(find.text('\$50'), findsOneWidget);
        expect(find.text('\$100'), findsOneWidget);
        expect(find.text('Enter Amount'), findsOneWidget);

        // Verify money-related icons are present
        expect(find.byIcon(Icons.attach_money), findsWidgets);
      });

      testWidgets('should create appropriate options for ambiguousType ambiguity', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: QuickReplyWidget.forAmbiguityType(
                ambiguityType: AmbiguityType.ambiguousType,
                options: const ['Expense', 'Income', 'Transfer'],
                onReplySelected: (option) {},
              ),
            ),
          ),
        );

        // Verify options are displayed
        expect(find.text('Expense'), findsOneWidget);
        expect(find.text('Income'), findsOneWidget);
        expect(find.text('Transfer'), findsOneWidget);
      });

      testWidgets('should create appropriate options for ambiguousCategory ambiguity', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: QuickReplyWidget.forAmbiguityType(
                ambiguityType: AmbiguityType.ambiguousCategory,
                options: const ['Food', 'Transport', 'Entertainment'],
                onReplySelected: (option) {},
              ),
            ),
          ),
        );

        // Verify options are displayed
        expect(find.text('Food'), findsOneWidget);
        expect(find.text('Transport'), findsOneWidget);
        expect(find.text('Entertainment'), findsOneWidget);
      });

      testWidgets('should handle unknown ambiguity types gracefully', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: QuickReplyWidget.forAmbiguityType(
                ambiguityType: 'unknown_type',
                options: const ['Option 1', 'Option 2'],
                onReplySelected: (option) {},
              ),
            ),
          ),
        );

        // Should still display options even with unknown ambiguity type
        expect(find.text('Option 1'), findsOneWidget);
        expect(find.text('Option 2'), findsOneWidget);
      });
    });

    group('Edge Cases and Error Handling', () {
      testWidgets('should handle empty options list gracefully', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: QuickReplyWidget(
                replyOptions: const [],
                onReplySelected: (option) {},
              ),
            ),
          ),
        );

        // Should not crash and should render empty container
        expect(find.byType(QuickReplyWidget), findsOneWidget);
      });

      testWidgets('should handle disabled state correctly', (WidgetTester tester) async {
        String? selectedOption;

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: QuickReplyWidget(
                replyOptions: const ['Option 1', 'Option 2'],
                onReplySelected: (option) {
                  selectedOption = option;
                },
                enabled: false,
              ),
            ),
          ),
        );

        // Verify options are displayed but disabled
        expect(find.text('Option 1'), findsOneWidget);
        expect(find.text('Option 2'), findsOneWidget);

        // Try to tap disabled button
        await tester.tap(find.text('Option 1'));
        expect(selectedOption, isNull); // Should not be called when disabled
      });

      testWidgets('should handle null or missing enhanced options', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: QuickReplyWidget(
                replyOptions: const ['Option 1', 'Option 2'],
                enhancedOptions: null,
                onReplySelected: (option) {},
                showIcons: true,
              ),
            ),
          ),
        );

        // Should fall back to basic text display
        expect(find.text('Option 1'), findsOneWidget);
        expect(find.text('Option 2'), findsOneWidget);
      });

      testWidgets('should handle semantic labels correctly', (WidgetTester tester) async {
        final options = [
          const QuickReplyOption(
            text: 'Save',
            semanticLabel: 'Save transaction',
          ),
        ];

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: QuickReplyWidget.withIcons(
                options: options,
                onReplySelected: (option) {},
              ),
            ),
          ),
        );

        // Verify semantic label is applied (accessibility testing)
        expect(find.bySemanticsLabel('Save transaction'), findsOneWidget);
      });

      testWidgets('should handle very long option text', (WidgetTester tester) async {
        const longText = 'This is a very long option text that might cause layout issues if not handled properly';

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: QuickReplyWidget(
                replyOptions: const [longText, 'Short'],
                onReplySelected: (option) {},
              ),
            ),
          ),
        );

        // Should display without overflow errors
        expect(find.text(longText), findsOneWidget);
        expect(find.text('Short'), findsOneWidget);
      });

      testWidgets('should maintain selection state during rebuilds', (WidgetTester tester) async {
        String? selectedOption;
        bool enabled = true;

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: StatefulBuilder(
                builder: (context, setState) {
                  return Column(
                    children: [
                      QuickReplyWidget(
                        replyOptions: const ['Option 1', 'Option 2'],
                        onReplySelected: (option) {
                          selectedOption = option;
                        },
                        enabled: enabled,
                      ),
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            enabled = !enabled;
                          });
                        },
                        child: const Text('Toggle Enabled'),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        );

        // Test initial state
        await tester.tap(find.text('Option 1'));
        expect(selectedOption, equals('Option 1'));

        // Toggle enabled state and verify widget rebuilds correctly
        await tester.tap(find.text('Toggle Enabled'));
        await tester.pump();

        expect(find.text('Option 1'), findsOneWidget);
        expect(find.text('Option 2'), findsOneWidget);
      });
    });
  });
}
