import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:dreamflow/models/transaction_model.dart';
import 'package:dreamflow/widgets/category_picker_dialog.dart';
import 'package:provider/provider.dart';

import '../helpers/test_helpers.dart';

void main() {
  group('CategoryPickerDialog Widget Tests', () {
    late TransactionProvider mockTransactionProvider;
    String? selectedCategoryId;

    setUp(() {
      mockTransactionProvider = TestHelpers.createMockTransactionProvider();
      selectedCategoryId = null;
    });

    Future<void> pumpDialog(WidgetTester tester, TransactionType type) async {
      await tester.pumpWidget(
        ChangeNotifierProvider.value(
          value: mockTransactionProvider,
          child: MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) => TextButton(
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (_) => CategoryPickerDialog(
                        transactionType: type,
                      ),
                    );
                  },
                  child: const Text('Show Dialog'),
                ),
              ),
            ),
          ),
        ),
      );

      // Show the dialog
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();
    }

    testWidgets('renders correctly and shows only expense categories', (WidgetTester tester) async {
      await pumpDialog(tester, TransactionType.expense);

      expect(find.text('Select a Category'), findsOneWidget);

      // Verify expense categories are visible
      expect(find.text('Food & Drink'), findsOneWidget);
      expect(find.text('Shopping'), findsOneWidget);
      expect(find.text('Transport'), findsOneWidget);

      // Verify income category is NOT visible
      expect(find.text('Salary'), findsNothing);
    });

    testWidgets('renders correctly and shows only income categories', (WidgetTester tester) async {
      await pumpDialog(tester, TransactionType.income);

      // Verify income category is visible
      expect(find.text('Salary'), findsOneWidget);

      // Verify expense categories are NOT visible
      expect(find.text('Food & Drink'), findsNothing);
      expect(find.text('Shopping'), findsNothing);
    });

    testWidgets('tapping a category calls onCategorySelected and closes dialog', (WidgetTester tester) async {
      await pumpDialog(tester, TransactionType.expense);

      await tester.tap(find.text('Shopping'));
      await tester.pumpAndSettle();

      // Verify callback was called with the correct category ID
      expect(selectedCategoryId, 'shopping');

      // Verify dialog is closed
      expect(find.byType(CategoryPickerDialog), findsNothing);
    });

    testWidgets('search filters the list of categories', (WidgetTester tester) async {
      await pumpDialog(tester, TransactionType.expense);

      // Initially, multiple categories are visible
      expect(find.text('Food & Drink'), findsOneWidget);
      expect(find.text('Shopping'), findsOneWidget);

      // Enter search query
      await tester.enterText(find.byType(TextField), 'Food');
      await tester.pumpAndSettle();

      // Verify only the matching category is visible
      expect(find.text('Food & Drink'), findsOneWidget);
      expect(find.text('Shopping'), findsNothing);
    });
  });
}
