import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:dreamflow/widgets/quick_reply_widget.dart';
import 'package:dreamflow/models/transaction_model.dart';
import '../helpers/test_helpers.dart';

void main() {
  group('QuickReplyWidget Specialized Variants Tests', () {
    group('TransactionTypeQuickReply', () {
      testWidgets('should create transaction type selection widget', (WidgetTester tester) async {
        TransactionType? selectedType;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: TransactionTypeQuickReply(
                onTypeSelected: (type) {
                  selectedType = type;
                },
              ),
            ),
          ),
        );

        // Verify all transaction type options are displayed
        expect(find.text('Expense'), findsOneWidget);
        expect(find.text('Income'), findsOneWidget);
        expect(find.text('Cancel'), findsOneWidget);
        
        // Verify callback hasn't been called yet
        expect(selectedType, isNull);
      });

      testWidgets('should handle expense selection', (WidgetTester tester) async {
        TransactionType? selectedType;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: TransactionTypeQuickReply(
                onTypeSelected: (type) {
                  selectedType = type;
                },
              ),
            ),
          ),
        );

        await tester.tap(find.text('Expense'));
        await tester.pump();

        expect(selectedType, equals(TransactionType.expense));
      });

      testWidgets('should handle income selection', (WidgetTester tester) async {
        TransactionType? selectedType;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: TransactionTypeQuickReply(
                onTypeSelected: (type) {
                  selectedType = type;
                },
              ),
            ),
          ),
        );

        await tester.tap(find.text('Income'));
        await tester.pump();

        expect(selectedType, equals(TransactionType.income));
      });

      testWidgets('should handle cancel selection', (WidgetTester tester) async {
        bool cancelCalled = false;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: TransactionTypeQuickReply(
                onTypeSelected: (type) {
                  // This shouldn't be called for cancel
                },
                onCancel: () {
                  cancelCalled = true;
                },
              ),
            ),
          ),
        );

        await tester.tap(find.text('Cancel'));
        await tester.pump();

        expect(cancelCalled, isTrue);
      });

      testWidgets('should use appropriate styling for transaction types', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(useMaterial3: true),
            home: Scaffold(
              body: TransactionTypeQuickReply(
                onTypeSelected: (type) {},
              ),
            ),
          ),
        );

        // Verify buttons are styled appropriately
        final expenseButton = find.ancestor(
          of: find.text('Expense'),
          matching: find.byType(ElevatedButton),
        );
        final incomeButton = find.ancestor(
          of: find.text('Income'),
          matching: find.byType(ElevatedButton),
        );
        final cancelButton = find.ancestor(
          of: find.text('Cancel'),
          matching: find.byType(ElevatedButton),
        );

        expect(expenseButton, findsOneWidget);
        expect(incomeButton, findsOneWidget);
        expect(cancelButton, findsOneWidget);
      });
    });

    group('CategoryQuickReply', () {
      testWidgets('should create category selection widget', (WidgetTester tester) async {
        final categories = TestHelpers.createTestCategories();
        Category? selectedCategory;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: CategoryQuickReply(
                categories: categories,
                onCategorySelected: (category) {
                  selectedCategory = category;
                },
              ),
            ),
          ),
        );

        // Verify category options are displayed
        for (final category in categories) {
          expect(find.text(category.name), findsOneWidget);
        }
        
        // Verify cancel option is displayed
        expect(find.text('Cancel'), findsOneWidget);
        
        // Verify callback hasn't been called yet
        expect(selectedCategory, isNull);
      });

      testWidgets('should handle category selection', (WidgetTester tester) async {
        final categories = TestHelpers.createTestCategories();
        Category? selectedCategory;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: CategoryQuickReply(
                categories: categories,
                onCategorySelected: (category) {
                  selectedCategory = category;
                },
              ),
            ),
          ),
        );

        // Tap on the first category
        await tester.tap(find.text(categories.first.name));
        await tester.pump();

        expect(selectedCategory, equals(categories.first));
      });

      testWidgets('should handle cancel selection', (WidgetTester tester) async {
        final categories = TestHelpers.createTestCategories();
        bool cancelCalled = false;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: CategoryQuickReply(
                categories: categories,
                onCategorySelected: (category) {
                  // This shouldn't be called for cancel
                },
                onCancel: () {
                  cancelCalled = true;
                },
              ),
            ),
          ),
        );

        await tester.tap(find.text('Cancel'));
        await tester.pump();

        expect(cancelCalled, isTrue);
      });

      testWidgets('should handle empty categories list', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: CategoryQuickReply(
                categories: [],
                onCategorySelected: (category) {},
              ),
            ),
          ),
        );

        // Only cancel button should be visible
        expect(find.text('Cancel'), findsOneWidget);
        expect(find.byType(ElevatedButton), findsOneWidget);
      });

      testWidgets('should limit displayed categories when too many', (WidgetTester tester) async {
        // Create many categories
        final manyCategories = List.generate(10, (index) => 
          Category(
            id: 'cat-$index',
            name: 'Category $index',
            type: TransactionType.expense,
            icon: 'icon_$index',
            color: 0xFF000000 + index,
          )
        );
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: CategoryQuickReply(
                categories: manyCategories,
                onCategorySelected: (category) {},
                maxDisplayedCategories: 5,
              ),
            ),
          ),
        );

        // Should show limited number of categories plus cancel
        final buttons = find.byType(ElevatedButton);
        expect(buttons, findsNWidgets(6)); // 5 categories + cancel
        
        // Verify cancel is always shown
        expect(find.text('Cancel'), findsOneWidget);
      });

      testWidgets('should display category icons when available', (WidgetTester tester) async {
        final categoriesWithIcons = [
          Category(
            id: 'food',
            name: 'Food',
            type: TransactionType.expense,
            icon: '🍔',
            color: 0xFFFF5722,
          ),
          Category(
            id: 'transport',
            name: 'Transport',
            type: TransactionType.expense,
            icon: '🚗',
            color: 0xFF2196F3,
          ),
        ];
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: CategoryQuickReply(
                categories: categoriesWithIcons,
                onCategorySelected: (category) {},
              ),
            ),
          ),
        );

        // Verify category names are displayed
        expect(find.text('Food'), findsOneWidget);
        expect(find.text('Transport'), findsOneWidget);
        
        // Note: Icon display testing depends on implementation
        // This verifies the basic structure is correct
        expect(find.byType(ElevatedButton), findsNWidgets(3)); // 2 categories + cancel
      });
    });

    group('Integration with QuickReplyWidget', () {
      testWidgets('should integrate properly with base QuickReplyWidget', (WidgetTester tester) async {
        // Test that specialized variants use the base QuickReplyWidget correctly
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: TransactionTypeQuickReply(
                onTypeSelected: (type) {},
              ),
            ),
          ),
        );

        // Verify base QuickReplyWidget is used
        expect(find.byType(QuickReplyWidget), findsOneWidget);
        
        // Verify proper options are passed
        expect(find.text('Expense'), findsOneWidget);
        expect(find.text('Income'), findsOneWidget);
        expect(find.text('Cancel'), findsOneWidget);
      });

      testWidgets('should maintain consistent styling across variants', (WidgetTester tester) async {
        final categories = TestHelpers.createTestCategories();
        
        // Test transaction type variant
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(useMaterial3: true),
            home: Scaffold(
              body: Column(
                children: [
                  TransactionTypeQuickReply(
                    onTypeSelected: (type) {},
                  ),
                  CategoryQuickReply(
                    categories: categories,
                    onCategorySelected: (category) {},
                  ),
                ],
              ),
            ),
          ),
        );

        // Verify both variants use consistent button styling
        final buttons = find.byType(ElevatedButton);
        expect(buttons, findsWidgets);
        
        // All buttons should have similar styling
        for (int i = 0; i < tester.widgetList(buttons).length; i++) {
          final button = tester.widget<ElevatedButton>(buttons.at(i));
          expect(button.style, isNotNull);
        }
      });
    });

    group('Error Handling', () {
      testWidgets('should handle null callbacks gracefully', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: TransactionTypeQuickReply(
                onTypeSelected: (type) {
                  // Empty callback
                },
              ),
            ),
          ),
        );

        // Should not crash
        expect(find.byType(TransactionTypeQuickReply), findsOneWidget);
        
        // Should still be interactive
        await tester.tap(find.text('Expense'));
        await tester.pump();
        
        expect(tester.takeException(), isNull);
      });

      testWidgets('should handle widget disposal correctly', (WidgetTester tester) async {
        bool disposed = false;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: TransactionTypeQuickReply(
                onTypeSelected: (type) {},
                onDispose: () {
                  disposed = true;
                },
              ),
            ),
          ),
        );

        // Remove the widget
        await tester.pumpWidget(
          const MaterialApp(
            home: Scaffold(
              body: SizedBox.shrink(),
            ),
          ),
        );

        // Note: Disposal testing depends on implementation
        // This verifies the widget can be removed without errors
        expect(find.byType(TransactionTypeQuickReply), findsNothing);
        expect(tester.takeException(), isNull);
      });
    });
  });
}
